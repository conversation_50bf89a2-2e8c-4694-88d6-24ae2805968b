# 医疗健康移动APP高保真原型

这是一个使用HTML、CSS、JavaScript和Tailwind CSS构建的医疗健康移动APP高保真原型。

## 🚀 项目特性

- **移动端优先设计** - 完全响应式布局，专为移动设备优化
- **现代化UI** - 使用Tailwind CSS构建的美观界面
- **高保真原型** - 接近真实APP的视觉效果和交互体验
- **完整功能流程** - 包含登录、预约、咨询、记录等完整业务流程
- **模拟数据** - 内置模拟数据，无需后端即可体验完整功能

## 📱 功能页面

### 核心页面
1. **登录/注册页面** (`index.html`) - 用户认证入口
2. **首页/仪表板** (`dashboard.html`) - 健康状态概览和快捷功能
3. **医生列表** (`doctors.html`) - 浏览和搜索医生
4. **预约挂号** (`appointment.html`) - 完整的预约流程
5. **在线咨询** (`consultation.html`) - 模拟聊天界面
6. **健康记录** (`records.html`) - 健康数据管理
7. **个人档案** (`profile.html`) - 个人健康信息
8. **设置页面** (`settings.html`) - 应用设置和账户管理

### 主要功能
- ✅ 用户登录/注册（支持快速体验）
- ✅ 健康数据可视化
- ✅ 医生搜索和筛选
- ✅ 预约挂号流程
- ✅ 实时聊天模拟
- ✅ 健康记录管理
- ✅ 个人信息管理
- ✅ 通知和设置

## 🛠️ 技术栈

- **HTML5** - 页面结构
- **CSS3** - 样式和动画
- **JavaScript (ES6+)** - 交互逻辑
- **Tailwind CSS** - UI框架（通过CDN引入）
- **本地存储** - 数据持久化

## 📁 项目结构

```
mp-ui2/
├── index.html              # 登录/注册页面
├── dashboard.html          # 首页/仪表板
├── doctors.html           # 医生列表页面
├── appointment.html       # 预约挂号页面
├── consultation.html      # 在线咨询页面
├── records.html          # 健康记录页面
├── profile.html          # 个人档案页面
├── settings.html         # 设置页面
├── assets/
│   ├── css/
│   │   └── custom.css    # 自定义样式
│   └── js/
│       └── app.js        # 通用JavaScript功能
└── README.md             # 项目说明
```

## 🚀 快速开始

### 1. 克隆或下载项目
```bash
# 如果使用Git
git clone [项目地址]
cd mp-ui2

# 或直接下载ZIP文件并解压
```

### 2. 启动项目
由于项目使用纯前端技术，可以通过以下方式运行：

#### 方法一：直接打开HTML文件
- 双击 `index.html` 文件在浏览器中打开

#### 方法二：使用本地服务器（推荐）
```bash
# 使用Python
python -m http.server 8000

# 使用Node.js (需要安装http-server)
npx http-server

# 使用PHP
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000`

### 3. 体验功能
1. 在登录页面点击"快速体验"按钮
2. 或者输入任意邮箱和密码进行登录
3. 浏览各个功能页面

## 📱 使用说明

### 登录体验
- **快速登录**：点击"快速体验"按钮即可直接进入应用
- **模拟登录**：输入任意邮箱和密码即可登录
- **注册功能**：填写注册表单即可创建账户

### 导航使用
- **底部导航**：在各主要页面间切换
- **顶部导航**：返回上一页或访问设置
- **页面内导航**：通过按钮和链接访问相关功能

### 数据说明
- 所有数据都是模拟数据，存储在浏览器本地
- 刷新页面不会丢失已添加的数据
- 可以在设置页面清除所有数据

## 🎨 设计特色

### 视觉设计
- **医疗主题色彩**：蓝色、绿色、白色为主色调
- **现代化图标**：使用Heroicons图标库
- **渐变效果**：多种渐变背景增强视觉效果
- **卡片设计**：统一的卡片样式和阴影效果

### 交互设计
- **流畅动画**：页面切换和按钮点击动画
- **响应式反馈**：悬停和点击状态反馈
- **加载状态**：模拟真实的加载过程
- **消息提示**：操作成功/失败的即时反馈

### 移动端优化
- **触摸友好**：按钮和链接区域足够大
- **滑动操作**：支持横向滑动选择
- **安全区域**：适配iPhone等设备的安全区域
- **性能优化**：轻量级代码，快速加载

## 🔧 自定义开发

### 修改样式
编辑 `assets/css/custom.css` 文件来自定义样式：
```css
/* 修改主题色 */
.medical-gradient {
  background: linear-gradient(135deg, #your-color 0%, #your-color 100%);
}
```

### 添加功能
在 `assets/js/app.js` 中添加新的JavaScript功能：
```javascript
// 添加新的功能函数
function newFeature() {
    // 你的代码
}
```

### 创建新页面
1. 复制现有页面作为模板
2. 修改页面内容和功能
3. 更新导航链接

## 📱 浏览器兼容性

- ✅ Chrome (推荐)
- ✅ Safari
- ✅ Firefox
- ✅ Edge
- ⚠️ IE 11+ (部分功能可能不支持)

## 📄 许可证

本项目仅用于学习和演示目的。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件

---

**注意**：这是一个原型项目，不包含真实的医疗数据或后端服务。所有功能都是模拟实现，仅用于展示和学习目的。
