<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部状态栏 -->
    <div class="safe-area-top bg-white">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span class="text-blue-600 font-semibold" id="userInitial">张</span>
                </div>
                <div>
                    <p class="text-gray-800 font-medium" id="userName">张三</p>
                    <p class="text-gray-500 text-sm">早上好！</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button class="p-2 bg-gray-100 rounded-full">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </button>
                <button class="p-2 bg-gray-100 rounded-full relative">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="px-4 pb-20">
        <!-- 健康状态卡片 -->
        <div class="mt-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white card-shadow">
            <h2 class="text-lg font-semibold mb-4">今日健康状态</h2>
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                    <p class="text-sm opacity-90">心率</p>
                    <p class="font-bold">72 bpm</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <p class="text-sm opacity-90">血压</p>
                    <p class="font-bold">120/80</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 11H7v9a2 2 0 002 2h8a2 2 0 002-2V9h-2m-1 0V7a4 4 0 00-8 0v2m5 2v6m-2-6v6m-2-6v6"/>
                        </svg>
                    </div>
                    <p class="text-sm opacity-90">步数</p>
                    <p class="font-bold">8,432</p>
                </div>
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">快捷服务</h3>
            <div class="grid grid-cols-4 gap-4">
                <button onclick="navigateToPage('appointment')" class="flex flex-col items-center p-4 bg-white rounded-xl card-shadow btn-animate">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-600">预约挂号</span>
                </button>
                
                <button onclick="navigateToPage('consultation')" class="flex flex-col items-center p-4 bg-white rounded-xl card-shadow btn-animate">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-600">在线咨询</span>
                </button>
                
                <button onclick="navigateToPage('records')" class="flex flex-col items-center p-4 bg-white rounded-xl card-shadow btn-animate">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-600">健康档案</span>
                </button>
                
                <button onclick="navigateToPage('doctors')" class="flex flex-col items-center p-4 bg-white rounded-xl card-shadow btn-animate">
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-600">找医生</span>
                </button>
            </div>
        </div>

        <!-- 最近预约 -->
        <div class="mt-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">最近预约</h3>
                <button class="text-blue-600 text-sm">查看全部</button>
            </div>
            
            <div class="bg-white rounded-xl p-4 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold">李</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">李医生 - 心内科</h4>
                        <p class="text-gray-500 text-sm">市人民医院</p>
                        <p class="text-blue-600 text-sm">明天 14:30</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">已确认</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 健康提醒 -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">健康提醒</h3>
            <div class="space-y-3">
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-800">记得按时服药</p>
                            <p class="text-xs text-yellow-600 mt-1">降压药 - 每日早晨8点</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-800">体检提醒</p>
                            <p class="text-xs text-blue-600 mt-1">距离下次体检还有30天</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-blue-600" data-page="dashboard">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="doctors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="consultation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="records">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="profile">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!checkLogin()) {
                window.location.href = 'index.html';
                return;
            }
            
            // 显示用户信息
            const user = getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.name;
                document.getElementById('userInitial').textContent = user.name.charAt(0);
            }
            
            // 模拟实时数据更新
            updateHealthData();
        });
        
        // 更新健康数据
        function updateHealthData() {
            // 这里可以添加实时数据更新逻辑
            console.log('健康数据已更新');
        }
    </script>
</body>
</html>
