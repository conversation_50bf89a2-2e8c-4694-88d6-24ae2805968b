<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 健康记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="safe-area-top bg-white card-shadow">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="p-2 -ml-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">健康记录</h1>
            <button class="p-2 -mr-2" onclick="addNewRecord()">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
        </div>
    </div>

    <div class="px-4 pb-20">
        <!-- 健康概览 -->
        <div class="mt-4 bg-white rounded-xl p-4 card-shadow">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">健康概览</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="health-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">血压</span>
                        <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-800">120/80</p>
                    <p class="text-green-600 text-sm">正常</p>
                </div>
                
                <div class="health-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">血糖</span>
                        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-800">5.6</p>
                    <p class="text-green-600 text-sm">正常</p>
                </div>
                
                <div class="health-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">体重</span>
                        <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-800">70kg</p>
                    <p class="text-green-600 text-sm">标准</p>
                </div>
                
                <div class="health-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">心率</span>
                        <svg class="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-800">72</p>
                    <p class="text-green-600 text-sm">正常</p>
                </div>
            </div>
        </div>

        <!-- 筛选选项 -->
        <div class="mt-6 flex space-x-3">
            <button class="filter-btn px-4 py-2 bg-blue-600 text-white rounded-full text-sm btn-animate" data-filter="all">
                全部
            </button>
            <button class="filter-btn px-4 py-2 bg-white text-gray-600 rounded-full text-sm border border-gray-200 btn-animate" data-filter="checkup">
                体检报告
            </button>
            <button class="filter-btn px-4 py-2 bg-white text-gray-600 rounded-full text-sm border border-gray-200 btn-animate" data-filter="monitor">
                监测数据
            </button>
            <button class="filter-btn px-4 py-2 bg-white text-gray-600 rounded-full text-sm border border-gray-200 btn-animate" data-filter="prescription">
                处方记录
            </button>
        </div>

        <!-- 记录列表 -->
        <div class="mt-4 space-y-4" id="recordsList">
            <!-- 记录项将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 添加记录模态框 -->
    <div id="addRecordModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-xl p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">添加健康记录</h3>
                    <button onclick="closeAddRecordModal()" class="p-1 text-gray-400">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <form id="addRecordForm">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">记录类型</label>
                        <select id="recordType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="monitor">监测数据</option>
                            <option value="checkup">体检报告</option>
                            <option value="prescription">处方记录</option>
                            <option value="symptom">症状记录</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">标题</label>
                        <input type="text" id="recordTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="请输入记录标题" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">数值/描述</label>
                        <textarea id="recordDescription" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="请输入详细描述或数值"></textarea>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-medium mb-2">日期</label>
                        <input type="date" id="recordDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" required>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="button" onclick="closeAddRecordModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg btn-animate">
                            取消
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg btn-animate hover:bg-blue-700">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="dashboard">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="doctors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="consultation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-blue-600" data-page="records">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="profile">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        let healthRecords = [];
        let filteredRecords = [];

        document.addEventListener('DOMContentLoaded', function() {
            loadHealthRecords();
            setupEventListeners();
            setTodayDate();
        });

        function loadHealthRecords() {
            // 加载模拟健康记录数据
            healthRecords = [
                {
                    id: 1,
                    type: 'checkup',
                    title: '年度体检报告',
                    description: '各项指标正常，建议定期复查',
                    date: '2024-01-15',
                    doctor: '李医生',
                    hospital: '市人民医院'
                },
                {
                    id: 2,
                    type: 'monitor',
                    title: '血压监测',
                    description: '120/80 mmHg',
                    date: '2024-01-10',
                    status: '正常'
                },
                {
                    id: 3,
                    type: 'monitor',
                    title: '血糖检测',
                    description: '5.6 mmol/L',
                    date: '2024-01-05',
                    status: '正常'
                },
                {
                    id: 4,
                    type: 'prescription',
                    title: '降压药处方',
                    description: '氨氯地平片 5mg，每日一次',
                    date: '2024-01-01',
                    doctor: '王医生'
                },
                {
                    id: 5,
                    type: 'symptom',
                    title: '头痛症状记录',
                    description: '轻微头痛，持续2小时后缓解',
                    date: '2023-12-28'
                }
            ];
            
            filteredRecords = [...healthRecords];
            renderRecords();
        }

        function setupEventListeners() {
            // 筛选按钮
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    filterRecords(filter);
                    
                    // 更新按钮状态
                    filterBtns.forEach(b => {
                        b.classList.remove('bg-blue-600', 'text-white');
                        b.classList.add('bg-white', 'text-gray-600', 'border', 'border-gray-200');
                    });
                    this.classList.remove('bg-white', 'text-gray-600', 'border', 'border-gray-200');
                    this.classList.add('bg-blue-600', 'text-white');
                });
            });

            // 添加记录表单
            document.getElementById('addRecordForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveNewRecord();
            });
        }

        function filterRecords(filter) {
            if (filter === 'all') {
                filteredRecords = [...healthRecords];
            } else {
                filteredRecords = healthRecords.filter(record => record.type === filter);
            }
            renderRecords();
        }

        function renderRecords() {
            const recordsList = document.getElementById('recordsList');
            recordsList.innerHTML = '';

            if (filteredRecords.length === 0) {
                recordsList.innerHTML = `
                    <div class="text-center py-8">
                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500">暂无记录</p>
                    </div>
                `;
                return;
            }

            filteredRecords.forEach(record => {
                const recordCard = createRecordCard(record);
                recordsList.appendChild(recordCard);
            });
        }

        function createRecordCard(record) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-xl p-4 card-shadow';
            
            const typeIcons = {
                'checkup': { icon: '📋', color: 'blue' },
                'monitor': { icon: '📊', color: 'green' },
                'prescription': { icon: '💊', color: 'purple' },
                'symptom': { icon: '🩺', color: 'orange' }
            };
            
            const typeInfo = typeIcons[record.type] || typeIcons['monitor'];
            
            card.innerHTML = `
                <div class="flex items-start">
                    <div class="w-12 h-12 bg-${typeInfo.color}-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <span class="text-2xl">${typeInfo.icon}</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-gray-800">${record.title}</h4>
                            <span class="text-sm text-gray-500">${formatDate(record.date)}</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">${record.description}</p>
                        ${record.doctor ? `<p class="text-blue-600 text-xs">医生：${record.doctor}</p>` : ''}
                        ${record.hospital ? `<p class="text-gray-500 text-xs">${record.hospital}</p>` : ''}
                        ${record.status ? `<span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full mt-2">${record.status}</span>` : ''}
                    </div>
                    <button onclick="deleteRecord(${record.id})" class="p-1 text-gray-400 hover:text-red-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            return card;
        }

        function addNewRecord() {
            document.getElementById('addRecordModal').classList.remove('hidden');
        }

        function closeAddRecordModal() {
            document.getElementById('addRecordModal').classList.add('hidden');
            document.getElementById('addRecordForm').reset();
        }

        function setTodayDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('recordDate').value = today;
        }

        function saveNewRecord() {
            const type = document.getElementById('recordType').value;
            const title = document.getElementById('recordTitle').value;
            const description = document.getElementById('recordDescription').value;
            const date = document.getElementById('recordDate').value;
            
            const newRecord = {
                id: Date.now(),
                type: type,
                title: title,
                description: description,
                date: date
            };
            
            healthRecords.unshift(newRecord);
            filteredRecords = [...healthRecords];
            renderRecords();
            closeAddRecordModal();
            showToast('记录添加成功！', 'success');
        }

        function deleteRecord(recordId) {
            if (confirm('确定要删除这条记录吗？')) {
                healthRecords = healthRecords.filter(record => record.id !== recordId);
                filteredRecords = filteredRecords.filter(record => record.id !== recordId);
                renderRecords();
                showToast('记录已删除', 'success');
            }
        }
    </script>
</body>
</html>
