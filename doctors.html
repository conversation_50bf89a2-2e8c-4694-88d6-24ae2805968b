<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 找医生</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="safe-area-top bg-white card-shadow">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="p-2 -ml-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">找医生</h1>
            <button class="p-2 -mr-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="px-4 py-4 bg-white">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索医生、科室或医院" class="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:bg-white border-0">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
    </div>

    <!-- 科室分类 -->
    <div class="px-4 py-4 bg-white mb-4">
        <h3 class="text-sm font-medium text-gray-800 mb-3">热门科室</h3>
        <div class="grid grid-cols-4 gap-3">
            <button class="specialty-btn flex flex-col items-center p-3 bg-gray-50 rounded-lg btn-animate" data-specialty="内科">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                </div>
                <span class="text-xs text-gray-600">内科</span>
            </button>
            
            <button class="specialty-btn flex flex-col items-center p-3 bg-gray-50 rounded-lg btn-animate" data-specialty="外科">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                    </svg>
                </div>
                <span class="text-xs text-gray-600">外科</span>
            </button>
            
            <button class="specialty-btn flex flex-col items-center p-3 bg-gray-50 rounded-lg btn-animate" data-specialty="儿科">
                <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <span class="text-xs text-gray-600">儿科</span>
            </button>
            
            <button class="specialty-btn flex flex-col items-center p-3 bg-gray-50 rounded-lg btn-animate" data-specialty="妇科">
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <span class="text-xs text-gray-600">妇科</span>
            </button>
        </div>
    </div>

    <!-- 筛选选项 -->
    <div class="px-4 mb-4">
        <div class="flex items-center space-x-3">
            <button class="filter-btn px-4 py-2 bg-white rounded-full text-sm border border-gray-200 btn-animate" data-filter="all">
                全部
            </button>
            <button class="filter-btn px-4 py-2 bg-white rounded-full text-sm border border-gray-200 btn-animate" data-filter="online">
                在线
            </button>
            <button class="filter-btn px-4 py-2 bg-white rounded-full text-sm border border-gray-200 btn-animate" data-filter="rating">
                评分最高
            </button>
            <button class="filter-btn px-4 py-2 bg-white rounded-full text-sm border border-gray-200 btn-animate" data-filter="price">
                价格最低
            </button>
        </div>
    </div>

    <!-- 医生列表 -->
    <div class="px-4 pb-20">
        <div id="doctorsList" class="space-y-4">
            <!-- 医生卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="dashboard">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-blue-600" data-page="doctors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="consultation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="records">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="profile">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        let allDoctors = [];
        let filteredDoctors = [];

        document.addEventListener('DOMContentLoaded', function() {
            loadDoctors();
            setupEventListeners();
        });

        function loadDoctors() {
            // 扩展医生数据
            allDoctors = [
                {
                    id: 1,
                    name: '李医生',
                    specialty: '心内科',
                    hospital: '市人民医院',
                    rating: 4.8,
                    experience: '15年',
                    avatar: 'https://via.placeholder.com/80x80/4facfe/ffffff?text=李',
                    status: 'online',
                    price: 200,
                    description: '擅长冠心病、高血压等心血管疾病的诊治'
                },
                {
                    id: 2,
                    name: '王医生',
                    specialty: '神经内科',
                    hospital: '中心医院',
                    rating: 4.9,
                    experience: '12年',
                    avatar: 'https://via.placeholder.com/80x80/10b981/ffffff?text=王',
                    status: 'busy',
                    price: 180,
                    description: '专注于脑血管疾病、癫痫等神经系统疾病'
                },
                {
                    id: 3,
                    name: '张医生',
                    specialty: '消化内科',
                    hospital: '第一医院',
                    rating: 4.7,
                    experience: '10年',
                    avatar: 'https://via.placeholder.com/80x80/f59e0b/ffffff?text=张',
                    status: 'offline',
                    price: 150,
                    description: '胃肠疾病、肝病诊疗经验丰富'
                },
                {
                    id: 4,
                    name: '陈医生',
                    specialty: '儿科',
                    hospital: '儿童医院',
                    rating: 4.9,
                    experience: '8年',
                    avatar: 'https://via.placeholder.com/80x80/ec4899/ffffff?text=陈',
                    status: 'online',
                    price: 120,
                    description: '儿童常见病、多发病诊治专家'
                },
                {
                    id: 5,
                    name: '刘医生',
                    specialty: '妇科',
                    hospital: '妇幼保健院',
                    rating: 4.8,
                    experience: '13年',
                    avatar: 'https://via.placeholder.com/80x80/8b5cf6/ffffff?text=刘',
                    status: 'online',
                    price: 160,
                    description: '妇科内分泌、不孕不育治疗专家'
                }
            ];
            
            filteredDoctors = [...allDoctors];
            renderDoctors();
        }

        function renderDoctors() {
            const doctorsList = document.getElementById('doctorsList');
            doctorsList.innerHTML = '';

            filteredDoctors.forEach(doctor => {
                const doctorCard = createDoctorCard(doctor);
                doctorsList.appendChild(doctorCard);
            });
        }

        function createDoctorCard(doctor) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-xl p-4 card-shadow doctor-card';
            
            const statusClass = doctor.status === 'online' ? 'status-online' : 
                               doctor.status === 'busy' ? 'status-busy' : 'status-offline';
            
            const statusText = doctor.status === 'online' ? '在线' : 
                              doctor.status === 'busy' ? '忙碌' : '离线';

            card.innerHTML = `
                <div class="flex items-start">
                    <div class="relative mr-4">
                        <img src="${doctor.avatar}" alt="${doctor.name}" class="w-16 h-16 rounded-full">
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 ${statusClass} rounded-full"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-800">${doctor.name}</h3>
                            <span class="text-sm text-gray-500">${statusText}</span>
                        </div>
                        <p class="text-blue-600 text-sm mb-1">${doctor.specialty}</p>
                        <p class="text-gray-500 text-sm mb-2">${doctor.hospital}</p>
                        <p class="text-gray-600 text-xs mb-3">${doctor.description}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex items-center mr-3">
                                    <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-600">${doctor.rating}</span>
                                </div>
                                <span class="text-sm text-gray-500">${doctor.experience}经验</span>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-semibold text-blue-600">¥${doctor.price}</p>
                                <button onclick="consultDoctor(${doctor.id})" class="mt-2 px-4 py-1 bg-blue-600 text-white text-sm rounded-full btn-animate hover:bg-blue-700">
                                    咨询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        function setupEventListeners() {
            // 搜索功能
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                filteredDoctors = allDoctors.filter(doctor => 
                    doctor.name.toLowerCase().includes(query) ||
                    doctor.specialty.toLowerCase().includes(query) ||
                    doctor.hospital.toLowerCase().includes(query)
                );
                renderDoctors();
            });

            // 科室筛选
            const specialtyBtns = document.querySelectorAll('.specialty-btn');
            specialtyBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const specialty = this.getAttribute('data-specialty');
                    filteredDoctors = allDoctors.filter(doctor => 
                        doctor.specialty.includes(specialty)
                    );
                    renderDoctors();
                    
                    // 更新按钮状态
                    specialtyBtns.forEach(b => b.classList.remove('bg-blue-100'));
                    this.classList.add('bg-blue-100');
                });
            });

            // 筛选功能
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    switch(filter) {
                        case 'all':
                            filteredDoctors = [...allDoctors];
                            break;
                        case 'online':
                            filteredDoctors = allDoctors.filter(doctor => doctor.status === 'online');
                            break;
                        case 'rating':
                            filteredDoctors = [...allDoctors].sort((a, b) => b.rating - a.rating);
                            break;
                        case 'price':
                            filteredDoctors = [...allDoctors].sort((a, b) => a.price - b.price);
                            break;
                    }
                    
                    renderDoctors();
                    
                    // 更新按钮状态
                    filterBtns.forEach(b => {
                        b.classList.remove('bg-blue-600', 'text-white');
                        b.classList.add('bg-white', 'text-gray-600');
                    });
                    this.classList.remove('bg-white', 'text-gray-600');
                    this.classList.add('bg-blue-600', 'text-white');
                });
            });
        }

        function consultDoctor(doctorId) {
            const doctor = allDoctors.find(d => d.id === doctorId);
            if (doctor) {
                showToast(`正在连接${doctor.name}...`, 'info');
                setTimeout(() => {
                    window.location.href = `consultation.html?doctor=${doctorId}`;
                }, 1000);
            }
        }
    </script>
</body>
</html>
