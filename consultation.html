<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 在线咨询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="safe-area-top bg-white card-shadow">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="p-2 -ml-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-semibold text-sm" id="doctorInitial">李</span>
                </div>
                <div>
                    <h1 class="text-lg font-semibold text-gray-800" id="doctorName">李医生</h1>
                    <p class="text-xs text-green-600" id="doctorStatus">在线</p>
                </div>
            </div>
            <button class="p-2 -mr-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="flex flex-col h-screen">
        <!-- 消息列表 -->
        <div class="flex-1 overflow-y-auto px-4 py-4 pb-24" id="messageList">
            <!-- 系统消息 -->
            <div class="text-center mb-4">
                <span class="inline-block px-3 py-1 bg-gray-200 text-gray-600 text-xs rounded-full">
                    咨询开始，请描述您的症状
                </span>
            </div>

            <!-- 医生欢迎消息 -->
            <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                    <span class="text-blue-600 font-semibold text-sm">李</span>
                </div>
                <div class="flex-1">
                    <div class="chat-bubble-doctor p-3 max-w-xs">
                        <p class="text-gray-800 text-sm">您好！我是李医生，请详细描述一下您的症状，我会为您提供专业的建议。</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">刚刚</p>
                </div>
            </div>
        </div>

        <!-- 快捷回复 -->
        <div id="quickReplies" class="px-4 py-2 bg-white border-t border-gray-200">
            <div class="flex space-x-2 overflow-x-auto">
                <button class="quick-reply-btn flex-shrink-0 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-full btn-animate">
                    头痛
                </button>
                <button class="quick-reply-btn flex-shrink-0 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-full btn-animate">
                    发烧
                </button>
                <button class="quick-reply-btn flex-shrink-0 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-full btn-animate">
                    咳嗽
                </button>
                <button class="quick-reply-btn flex-shrink-0 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-full btn-animate">
                    胃痛
                </button>
                <button class="quick-reply-btn flex-shrink-0 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-full btn-animate">
                    失眠
                </button>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="safe-area-bottom bg-white border-t border-gray-200 px-4 py-3">
            <div class="flex items-center space-x-3">
                <button class="p-2 text-gray-500">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                </button>
                <div class="flex-1 relative">
                    <input type="text" id="messageInput" placeholder="输入您的症状或问题..." class="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button id="sendButton" class="p-2 bg-blue-600 text-white rounded-full btn-animate hover:bg-blue-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="dashboard">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="doctors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-blue-600" data-page="consultation">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="records">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="profile">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        let currentDoctor = {
            id: 1,
            name: '李医生',
            specialty: '心内科',
            avatar: 'https://via.placeholder.com/80x80/4facfe/ffffff?text=李'
        };

        const doctorResponses = {
            '头痛': '头痛可能有多种原因，请问您的头痛是什么时候开始的？疼痛的性质是怎样的？',
            '发烧': '发烧是身体的一种保护性反应。请问您现在的体温是多少？还有其他症状吗？',
            '咳嗽': '咳嗽可能是感冒、过敏或其他呼吸道疾病的症状。请问咳嗽多久了？有痰吗？',
            '胃痛': '胃痛需要注意饮食。请问疼痛是在什么时候？与进食有关系吗？',
            '失眠': '失眠会影响身体健康。请问您最近的睡眠情况如何？有什么压力或焦虑吗？',
            'default': '我了解您的情况。根据您的描述，建议您注意休息，如果症状持续或加重，请及时就医。还有其他问题吗？'
        };

        document.addEventListener('DOMContentLoaded', function() {
            // 检查URL参数获取医生信息
            const urlParams = new URLSearchParams(window.location.search);
            const doctorId = urlParams.get('doctor');
            
            if (doctorId) {
                loadDoctorInfo(doctorId);
            }
            
            setupEventListeners();
            scrollToBottom();
        });

        function loadDoctorInfo(doctorId) {
            const doctors = JSON.parse(localStorage.getItem('mockDoctors') || '[]');
            const doctor = doctors.find(d => d.id == doctorId);
            
            if (doctor) {
                currentDoctor = doctor;
                document.getElementById('doctorName').textContent = doctor.name;
                document.getElementById('doctorInitial').textContent = doctor.name.charAt(0);
                document.getElementById('doctorStatus').textContent = doctor.status === 'online' ? '在线' : '离线';
            }
        }

        function setupEventListeners() {
            // 发送消息
            const sendButton = document.getElementById('sendButton');
            const messageInput = document.getElementById('messageInput');
            
            sendButton.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // 快捷回复
            const quickReplyBtns = document.querySelectorAll('.quick-reply-btn');
            quickReplyBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const message = this.textContent;
                    sendQuickReply(message);
                });
            });
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (message) {
                addUserMessage(message);
                messageInput.value = '';
                
                // 模拟医生回复
                setTimeout(() => {
                    addDoctorMessage(message);
                }, 1000 + Math.random() * 2000);
            }
        }

        function sendQuickReply(message) {
            addUserMessage(message);
            
            // 模拟医生回复
            setTimeout(() => {
                addDoctorMessage(message);
            }, 1000 + Math.random() * 2000);
        }

        function addUserMessage(message) {
            const messageList = document.getElementById('messageList');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start justify-end mb-4';
            
            messageDiv.innerHTML = `
                <div class="flex-1 text-right">
                    <div class="chat-bubble-user text-white p-3 max-w-xs ml-auto">
                        <p class="text-sm">${message}</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">刚刚</p>
                </div>
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2 flex-shrink-0">
                    <span class="text-gray-600 font-semibold text-sm">我</span>
                </div>
            `;
            
            messageList.appendChild(messageDiv);
            scrollToBottom();
        }

        function addDoctorMessage(userMessage) {
            const messageList = document.getElementById('messageList');
            
            // 显示正在输入
            const typingDiv = document.createElement('div');
            typingDiv.className = 'flex items-start mb-4';
            typingDiv.id = 'typing-indicator';
            typingDiv.innerHTML = `
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                    <span class="text-blue-600 font-semibold text-sm">${currentDoctor.name.charAt(0)}</span>
                </div>
                <div class="flex-1">
                    <div class="chat-bubble-doctor p-3 max-w-xs">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                </div>
            `;
            
            messageList.appendChild(typingDiv);
            scrollToBottom();
            
            // 2秒后显示实际回复
            setTimeout(() => {
                messageList.removeChild(typingDiv);
                
                const response = getDoctorResponse(userMessage);
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start mb-4';
                
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <span class="text-blue-600 font-semibold text-sm">${currentDoctor.name.charAt(0)}</span>
                    </div>
                    <div class="flex-1">
                        <div class="chat-bubble-doctor p-3 max-w-xs">
                            <p class="text-gray-800 text-sm">${response}</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">刚刚</p>
                    </div>
                `;
                
                messageList.appendChild(messageDiv);
                scrollToBottom();
            }, 2000);
        }

        function getDoctorResponse(userMessage) {
            // 简单的关键词匹配
            for (const keyword in doctorResponses) {
                if (userMessage.includes(keyword)) {
                    return doctorResponses[keyword];
                }
            }
            return doctorResponses.default;
        }

        function scrollToBottom() {
            const messageList = document.getElementById('messageList');
            messageList.scrollTop = messageList.scrollHeight;
        }
    </script>
</body>
</html>
