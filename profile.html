<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 个人档案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="safe-area-top bg-white card-shadow">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="p-2 -ml-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">个人档案</h1>
            <button onclick="editProfile()" class="p-2 -mr-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </button>
        </div>
    </div>

    <div class="px-4 pb-20">
        <!-- 个人信息卡片 -->
        <div class="mt-4 bg-white rounded-xl p-6 card-shadow">
            <div class="flex items-center mb-6">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span class="text-blue-600 font-bold text-2xl" id="userAvatar">张</span>
                </div>
                <div class="flex-1">
                    <h2 class="text-xl font-bold text-gray-800" id="userName">张三</h2>
                    <p class="text-gray-500" id="userAge">28岁 • 男</p>
                    <p class="text-blue-600 text-sm" id="userPhone">138****8888</p>
                </div>
            </div>
            
            <!-- 基本信息 -->
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <p class="text-gray-600 text-sm">身高</p>
                    <p class="font-semibold text-gray-800" id="userHeight">175cm</p>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <p class="text-gray-600 text-sm">体重</p>
                    <p class="font-semibold text-gray-800" id="userWeight">70kg</p>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <p class="text-gray-600 text-sm">血型</p>
                    <p class="font-semibold text-gray-800" id="userBloodType">A型</p>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <p class="text-gray-600 text-sm">BMI</p>
                    <p class="font-semibold text-gray-800" id="userBMI">22.9</p>
                </div>
            </div>
        </div>

        <!-- 健康状态 -->
        <div class="mt-6 bg-white rounded-xl p-4 card-shadow">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">健康状态</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">血压</p>
                            <p class="text-sm text-gray-500">最近测量：今天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">120/80</p>
                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">血糖</p>
                            <p class="text-sm text-gray-500">最近测量：昨天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">5.6 mmol/L</p>
                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">胆固醇</p>
                            <p class="text-sm text-gray-500">最近测量：1周前</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">4.2 mmol/L</p>
                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">正常</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 过敏史 -->
        <div class="mt-6 bg-white rounded-xl p-4 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">过敏史</h3>
                <button onclick="addAllergy()" class="text-blue-600 text-sm">添加</button>
            </div>
            <div id="allergyList" class="space-y-2">
                <div class="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                    <span class="text-red-800">青霉素</span>
                    <button onclick="removeAllergy(this)" class="text-red-500 text-sm">删除</button>
                </div>
                <div class="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                    <span class="text-red-800">海鲜</span>
                    <button onclick="removeAllergy(this)" class="text-red-500 text-sm">删除</button>
                </div>
            </div>
        </div>

        <!-- 家族病史 -->
        <div class="mt-6 bg-white rounded-xl p-4 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">家族病史</h3>
                <button onclick="addFamilyHistory()" class="text-blue-600 text-sm">添加</button>
            </div>
            <div id="familyHistoryList" class="space-y-2">
                <div class="flex items-center justify-between p-2 bg-yellow-50 rounded-lg">
                    <div>
                        <span class="text-yellow-800 font-medium">高血压</span>
                        <span class="text-yellow-600 text-sm ml-2">父亲</span>
                    </div>
                    <button onclick="removeFamilyHistory(this)" class="text-yellow-600 text-sm">删除</button>
                </div>
                <div class="flex items-center justify-between p-2 bg-yellow-50 rounded-lg">
                    <div>
                        <span class="text-yellow-800 font-medium">糖尿病</span>
                        <span class="text-yellow-600 text-sm ml-2">母亲</span>
                    </div>
                    <button onclick="removeFamilyHistory(this)" class="text-yellow-600 text-sm">删除</button>
                </div>
            </div>
        </div>

        <!-- 紧急联系人 -->
        <div class="mt-6 bg-white rounded-xl p-4 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">紧急联系人</h3>
                <button onclick="editEmergencyContact()" class="text-blue-600 text-sm">编辑</button>
            </div>
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-800" id="emergencyName">李女士</p>
                    <p class="text-gray-500 text-sm" id="emergencyRelation">配偶</p>
                    <p class="text-blue-600 text-sm" id="emergencyPhone">139****9999</p>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="mt-6 bg-white rounded-xl p-4 card-shadow">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">更多功能</h3>
            <div class="space-y-3">
                <button onclick="navigateToPage('records')" class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg btn-animate">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-gray-800">健康记录</span>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button onclick="navigateToPage('appointment')" class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg btn-animate">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-gray-800">预约记录</span>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button onclick="navigateToPage('settings')" class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg btn-animate">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-gray-800">设置</span>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="dashboard">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="doctors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="consultation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="records">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-blue-600" data-page="profile">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
            calculateBMI();
        });

        function loadUserProfile() {
            const user = getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.name || '张三';
                document.getElementById('userAvatar').textContent = (user.name || '张').charAt(0);
                document.getElementById('userAge').textContent = `${user.age || 28}岁 • ${user.gender || '男'}`;
                document.getElementById('userPhone').textContent = user.phone || '138****8888';
                document.getElementById('userHeight').textContent = user.height || '175cm';
                document.getElementById('userWeight').textContent = user.weight || '70kg';
                document.getElementById('userBloodType').textContent = user.bloodType || 'A型';
            }
        }

        function calculateBMI() {
            const user = getCurrentUser();
            if (user && user.height && user.weight) {
                const height = parseFloat(user.height) / 100; // 转换为米
                const weight = parseFloat(user.weight);
                const bmi = (weight / (height * height)).toFixed(1);
                document.getElementById('userBMI').textContent = bmi;
            }
        }

        function editProfile() {
            showToast('编辑功能开发中...', 'info');
        }

        function addAllergy() {
            const allergy = prompt('请输入过敏物质：');
            if (allergy && allergy.trim()) {
                const allergyList = document.getElementById('allergyList');
                const allergyItem = document.createElement('div');
                allergyItem.className = 'flex items-center justify-between p-2 bg-red-50 rounded-lg';
                allergyItem.innerHTML = `
                    <span class="text-red-800">${allergy.trim()}</span>
                    <button onclick="removeAllergy(this)" class="text-red-500 text-sm">删除</button>
                `;
                allergyList.appendChild(allergyItem);
                showToast('过敏史添加成功', 'success');
            }
        }

        function removeAllergy(button) {
            if (confirm('确定要删除这条过敏史吗？')) {
                button.parentElement.remove();
                showToast('过敏史已删除', 'success');
            }
        }

        function addFamilyHistory() {
            const disease = prompt('请输入疾病名称：');
            if (disease && disease.trim()) {
                const relation = prompt('请输入患病亲属关系（如：父亲、母亲等）：');
                if (relation && relation.trim()) {
                    const familyHistoryList = document.getElementById('familyHistoryList');
                    const historyItem = document.createElement('div');
                    historyItem.className = 'flex items-center justify-between p-2 bg-yellow-50 rounded-lg';
                    historyItem.innerHTML = `
                        <div>
                            <span class="text-yellow-800 font-medium">${disease.trim()}</span>
                            <span class="text-yellow-600 text-sm ml-2">${relation.trim()}</span>
                        </div>
                        <button onclick="removeFamilyHistory(this)" class="text-yellow-600 text-sm">删除</button>
                    `;
                    familyHistoryList.appendChild(historyItem);
                    showToast('家族病史添加成功', 'success');
                }
            }
        }

        function removeFamilyHistory(button) {
            if (confirm('确定要删除这条家族病史吗？')) {
                button.parentElement.remove();
                showToast('家族病史已删除', 'success');
            }
        }

        function editEmergencyContact() {
            showToast('紧急联系人编辑功能开发中...', 'info');
        }
    </script>
</body>
</html>
