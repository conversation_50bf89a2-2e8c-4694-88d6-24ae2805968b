<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康医疗 - 预约挂号</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="assets/css/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4facfe',
                        secondary: '#00f2fe'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="safe-area-top bg-white card-shadow">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="p-2 -ml-2">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">预约挂号</h1>
            <div class="w-10"></div>
        </div>
    </div>

    <div class="px-4 pb-20">
        <!-- 选择医院 -->
        <div class="mt-4 bg-white rounded-xl p-4 card-shadow">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择医院</h3>
            <div class="space-y-3">
                <button class="hospital-btn w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg btn-animate" data-hospital="市人民医院">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <h4 class="font-medium text-gray-800">市人民医院</h4>
                            <p class="text-gray-500 text-sm">三甲医院 • 距离2.3km</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button class="hospital-btn w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg btn-animate" data-hospital="中心医院">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <h4 class="font-medium text-gray-800">中心医院</h4>
                            <p class="text-gray-500 text-sm">三甲医院 • 距离1.8km</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <button class="hospital-btn w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg btn-animate" data-hospital="第一医院">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <h4 class="font-medium text-gray-800">第一医院</h4>
                            <p class="text-gray-500 text-sm">三甲医院 • 距离3.1km</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 选择科室 -->
        <div id="departmentSection" class="mt-4 bg-white rounded-xl p-4 card-shadow hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择科室</h3>
            <div class="grid grid-cols-2 gap-3" id="departmentList">
                <!-- 科室列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 选择医生 -->
        <div id="doctorSection" class="mt-4 bg-white rounded-xl p-4 card-shadow hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择医生</h3>
            <div id="doctorList" class="space-y-3">
                <!-- 医生列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 选择时间 -->
        <div id="timeSection" class="mt-4 bg-white rounded-xl p-4 card-shadow hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择时间</h3>
            
            <!-- 日期选择 -->
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">选择日期</h4>
                <div class="flex space-x-2 overflow-x-auto" id="dateList">
                    <!-- 日期列表将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 时间段选择 -->
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">选择时间段</h4>
                <div class="grid grid-cols-3 gap-2" id="timeSlotList">
                    <!-- 时间段列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 预约信息确认 -->
        <div id="confirmSection" class="mt-4 bg-white rounded-xl p-4 card-shadow hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">预约信息确认</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">医院</span>
                    <span class="font-medium" id="selectedHospital">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">科室</span>
                    <span class="font-medium" id="selectedDepartment">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">医生</span>
                    <span class="font-medium" id="selectedDoctor">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">时间</span>
                    <span class="font-medium" id="selectedTime">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">挂号费</span>
                    <span class="font-medium text-blue-600" id="appointmentFee">¥50</span>
                </div>
            </div>
            
            <button id="confirmAppointment" class="w-full mt-6 bg-blue-600 text-white py-3 rounded-lg font-medium btn-animate hover:bg-blue-700">
                确认预约
            </button>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bottom-nav safe-area-bottom">
        <div class="flex items-center justify-around py-2">
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="dashboard">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="doctors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">医生</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="consultation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs mt-1">咨询</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="records">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs mt-1">记录</span>
            </button>
            
            <button class="nav-item flex flex-col items-center p-2 text-gray-400" data-page="profile">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        let appointmentData = {
            hospital: '',
            department: '',
            doctor: '',
            date: '',
            timeSlot: '',
            fee: 50
        };

        const departments = [
            '内科', '外科', '儿科', '妇科', '骨科', '眼科',
            '耳鼻喉科', '皮肤科', '神经科', '心理科', '急诊科', '体检科'
        ];

        const doctors = {
            '内科': [
                { name: '李医生', title: '主任医师', fee: 50 },
                { name: '王医生', title: '副主任医师', fee: 40 },
                { name: '张医生', title: '主治医师', fee: 30 }
            ],
            '外科': [
                { name: '陈医生', title: '主任医师', fee: 60 },
                { name: '刘医生', title: '副主任医师', fee: 50 }
            ],
            '儿科': [
                { name: '赵医生', title: '主任医师', fee: 45 },
                { name: '孙医生', title: '主治医师', fee: 35 }
            ]
        };

        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            generateDates();
        });

        function setupEventListeners() {
            // 医院选择
            const hospitalBtns = document.querySelectorAll('.hospital-btn');
            hospitalBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const hospital = this.getAttribute('data-hospital');
                    selectHospital(hospital);
                });
            });

            // 确认预约
            document.getElementById('confirmAppointment').addEventListener('click', function() {
                confirmAppointment();
            });
        }

        function selectHospital(hospital) {
            appointmentData.hospital = hospital;
            document.getElementById('selectedHospital').textContent = hospital;
            
            // 显示科室选择
            showDepartments();
            document.getElementById('departmentSection').classList.remove('hidden');
            
            // 更新医院按钮状态
            const hospitalBtns = document.querySelectorAll('.hospital-btn');
            hospitalBtns.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
                if (btn.getAttribute('data-hospital') === hospital) {
                    btn.classList.add('border-blue-500', 'bg-blue-50');
                }
            });
        }

        function showDepartments() {
            const departmentList = document.getElementById('departmentList');
            departmentList.innerHTML = '';

            departments.forEach(dept => {
                const btn = document.createElement('button');
                btn.className = 'department-btn p-3 border border-gray-200 rounded-lg text-center btn-animate hover:border-blue-500 hover:bg-blue-50';
                btn.textContent = dept;
                btn.addEventListener('click', () => selectDepartment(dept));
                departmentList.appendChild(btn);
            });
        }

        function selectDepartment(department) {
            appointmentData.department = department;
            document.getElementById('selectedDepartment').textContent = department;
            
            // 显示医生选择
            showDoctors(department);
            document.getElementById('doctorSection').classList.remove('hidden');
            
            // 更新科室按钮状态
            const departmentBtns = document.querySelectorAll('.department-btn');
            departmentBtns.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
                if (btn.textContent === department) {
                    btn.classList.add('border-blue-500', 'bg-blue-50');
                }
            });
        }

        function showDoctors(department) {
            const doctorList = document.getElementById('doctorList');
            doctorList.innerHTML = '';

            const departmentDoctors = doctors[department] || doctors['内科'];
            
            departmentDoctors.forEach(doctor => {
                const doctorCard = document.createElement('button');
                doctorCard.className = 'doctor-btn w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg btn-animate hover:border-blue-500 hover:bg-blue-50';
                doctorCard.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-blue-600 font-semibold">${doctor.name.charAt(0)}</span>
                        </div>
                        <div class="text-left">
                            <h4 class="font-medium text-gray-800">${doctor.name}</h4>
                            <p class="text-gray-500 text-sm">${doctor.title}</p>
                        </div>
                    </div>
                    <span class="text-blue-600 font-medium">¥${doctor.fee}</span>
                `;
                doctorCard.addEventListener('click', () => selectDoctor(doctor));
                doctorList.appendChild(doctorCard);
            });
        }

        function selectDoctor(doctor) {
            appointmentData.doctor = doctor.name;
            appointmentData.fee = doctor.fee;
            document.getElementById('selectedDoctor').textContent = doctor.name;
            document.getElementById('appointmentFee').textContent = `¥${doctor.fee}`;
            
            // 显示时间选择
            document.getElementById('timeSection').classList.remove('hidden');
            generateTimeSlots();
            
            // 更新医生按钮状态
            const doctorBtns = document.querySelectorAll('.doctor-btn');
            doctorBtns.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
                if (btn.querySelector('h4').textContent === doctor.name) {
                    btn.classList.add('border-blue-500', 'bg-blue-50');
                }
            });
        }

        function generateDates() {
            const dateList = document.getElementById('dateList');
            const today = new Date();
            
            for (let i = 0; i < 7; i++) {
                const date = new Date(today);
                date.setDate(today.getDate() + i);
                
                const dateBtn = document.createElement('button');
                dateBtn.className = 'date-btn flex-shrink-0 px-4 py-3 border border-gray-200 rounded-lg text-center btn-animate hover:border-blue-500 hover:bg-blue-50';
                dateBtn.innerHTML = `
                    <div class="text-sm text-gray-600">${date.getMonth() + 1}月${date.getDate()}日</div>
                    <div class="text-xs text-gray-500">${['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]}</div>
                `;
                dateBtn.addEventListener('click', () => selectDate(date));
                dateList.appendChild(dateBtn);
            }
        }

        function selectDate(date) {
            appointmentData.date = date.toLocaleDateString('zh-CN');
            
            // 更新日期按钮状态
            const dateBtns = document.querySelectorAll('.date-btn');
            dateBtns.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
            });
            event.target.closest('.date-btn').classList.add('border-blue-500', 'bg-blue-50');
        }

        function generateTimeSlots() {
            const timeSlotList = document.getElementById('timeSlotList');
            timeSlotList.innerHTML = '';
            
            const timeSlots = [
                '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
                '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'
            ];
            
            timeSlots.forEach(time => {
                const timeBtn = document.createElement('button');
                timeBtn.className = 'time-slot p-2 border border-gray-200 rounded-lg text-sm btn-animate';
                timeBtn.textContent = time;
                timeBtn.addEventListener('click', () => selectTimeSlot(time));
                timeSlotList.appendChild(timeBtn);
            });
        }

        function selectTimeSlot(time) {
            appointmentData.timeSlot = time;
            document.getElementById('selectedTime').textContent = `${appointmentData.date} ${time}`;
            
            // 显示确认信息
            document.getElementById('confirmSection').classList.remove('hidden');
            
            // 更新时间按钮状态
            const timeBtns = document.querySelectorAll('.time-slot');
            timeBtns.forEach(btn => {
                btn.classList.remove('selected');
                if (btn.textContent === time) {
                    btn.classList.add('selected');
                }
            });
        }

        function confirmAppointment() {
            const btn = document.getElementById('confirmAppointment');
            const hideLoading = showLoading(btn);
            
            setTimeout(() => {
                hideLoading();
                showToast('预约成功！', 'success');
                
                // 保存预约信息
                const appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
                appointments.push({
                    id: Date.now(),
                    ...appointmentData,
                    status: '已确认',
                    createTime: new Date().toISOString()
                });
                localStorage.setItem('appointments', JSON.stringify(appointments));
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            }, 2000);
        }
    </script>
</body>
</html>
